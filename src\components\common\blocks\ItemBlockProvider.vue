<script setup lang="ts">
import { provide, watch } from 'vue';
import { createItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock } from 'src/types/models';

const props = defineProps<{
  blockId: number;
  itemBlock?: ItemBlock;
}>();

// Create the store for this block, passing the itemBlock data
const blockStore = createItemBlockStore(props.blockId, props.itemBlock);
const storeInstance = blockStore();

// Watch for changes in itemBlock and reinitialize options when they change
watch(
  () => props.itemBlock,
  (newItemBlock, oldItemBlock) => {
    if (!newItemBlock || !oldItemBlock) return;

    // Check if the type changed or options array changed
    const typeChanged = newItemBlock.type !== oldItemBlock.type;
    const optionsChanged =
      JSON.stringify(newItemBlock.options) !== JSON.stringify(oldItemBlock.options);

    // Check if questions array changed (for GRID and TEXTFIELD types)
    const questionsChanged =
      JSON.stringify(newItemBlock.questions) !== JSON.stringify(oldItemBlock.questions);

    // Check if only isRequired changed (should NOT trigger reinitialization)
    const onlyIsRequiredChanged =
      !typeChanged &&
      !optionsChanged &&
      !questionsChanged &&
      newItemBlock.isRequired !== oldItemBlock.isRequired;

    // Only reinitialize if there are structural changes, not just isRequired changes
    if ((typeChanged || optionsChanged || questionsChanged) && !onlyIsRequiredChanged) {
      console.log(
        '🔄 [ItemBlockProvider] Detected structural itemBlock changes, reinitializing store:',
        {
          blockId: props.blockId,
          typeChanged,
          optionsChanged,
          questionsChanged,
          oldType: oldItemBlock.type,
          newType: newItemBlock.type,
          oldOptionsCount: oldItemBlock.options?.length || 0,
          newOptionsCount: newItemBlock.options?.length || 0,
          oldQuestionsCount: oldItemBlock.questions?.length || 0,
          newQuestionsCount: newItemBlock.questions?.length || 0,
        },
      );

      // Special handling for type changes - ensure options are properly reset
      if (typeChanged) {
        console.log('🔄 [ItemBlockProvider] Type changed - forcing option reset:', {
          blockId: props.blockId,
          oldType: oldItemBlock.type,
          newType: newItemBlock.type,
          oldOptionsCount: oldItemBlock.options?.length || 0,
          newOptionsCount: newItemBlock.options?.length || 0,
          newOptionsFromBackend:
            newItemBlock.options?.map((opt) => ({
              id: opt.id,
              optionText: opt.optionText,
              value: opt.value,
              sequence: opt.sequence,
            })) || [],
        });
      }

      // Reinitialize the store with new itemBlock data
      console.log('🔄 [ItemBlockProvider] Calling store reinitializeWithItemBlock...');
      storeInstance.reinitializeWithItemBlock(newItemBlock);
      console.log('✅ [ItemBlockProvider] Store reinitialization completed');
    } else if (onlyIsRequiredChanged) {
      console.log(
        '🔄 [ItemBlockProvider] Only isRequired changed, skipping store reinitialization:',
        {
          blockId: props.blockId,
          oldIsRequired: oldItemBlock.isRequired,
          newIsRequired: newItemBlock.isRequired,
        },
      );
    }
  },
  { deep: true },
);

// Provide the store to child components
provide('blockStore', storeInstance);
</script>

<template>
  <slot />
</template>
